/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8fafc;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.25rem 0;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}



.logo h1 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.subtitle {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 400;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.nav {
    display: flex;
    gap: 0.6rem;
    align-items: center;
    margin-left: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    padding: 0.85rem 1.2rem;
    border-radius: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.6rem;
    position: relative;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
    overflow: hidden;
    min-width: 140px;
    height: 50px;
    text-align: center;
    white-space: nowrap;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link i {
    font-size: 1rem;
    opacity: 0.8;
    transition: all 0.3s ease;
    z-index: 1;
    position: relative;
}

.nav-link span {
    z-index: 1;
    position: relative;
}

.nav-link:hover {
    background-color: rgba(255,255,255,0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    border-color: rgba(255,255,255,0.3);
}

.nav-link:hover i {
    opacity: 1;
    transform: scale(1.1);
}

.nav-link.active {
    background-color: rgba(255,255,255,0.25);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    border-color: rgba(255,255,255,0.4);
    transform: translateY(-1px);
}

.nav-link.active i {
    opacity: 1;
    transform: scale(1.05);
}

.nav-link:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
}

/* Main content */
.main {
    padding: 3rem 0;
}

.hero {
    text-align: center;
    margin-bottom: 3rem;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 1rem;
}

.hero-description {
    font-size: 1.125rem;
    color: #718096;
    max-width: 600px;
    margin: 0 auto;
}

/* Controls */
.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    gap: 1rem;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.875rem;
    background: white;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filters {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.filters select {
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    font-size: 0.875rem;
    cursor: pointer;
}

.refresh-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.refresh-btn:hover {
    background: #5a67d8;
}

/* Leaderboard table */
.leaderboard-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    overflow: hidden;
    margin-bottom: 2rem;
}

.table-wrapper {
    overflow-x: auto;
}

.leaderboard-table {
    width: 100%;
    min-width: 1400px; /* Ensure minimum width for all columns */
    border-collapse: collapse;
}

.leaderboard-table th {
    background: #f7fafc;
    padding: 0.75rem;
    text-align: center;
    font-weight: 600;
    color: #2d3748;
    border-bottom: 1px solid #e2e8f0;
    white-space: nowrap;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s;
    font-size: 0.875rem;
    line-height: 1.2;
    vertical-align: middle;
}

.leaderboard-table th:hover {
    background: #edf2f7;
}

.leaderboard-table th[data-column="rank"],
.leaderboard-table th[data-column="model"] {
    text-align: left;
}

/* Sticky model column */
.leaderboard-table th[data-column="model"],
.leaderboard-table td.model-cell {
    position: sticky;
    left: 0;
    background: #f7fafc;
    z-index: 10;
    border-right: 2px solid #e2e8f0;
}

.leaderboard-table tbody tr:hover td.model-cell {
    background: #f8fafc;
}

.leaderboard-table th i {
    margin-left: 0.5rem;
    opacity: 0.5;
    transition: opacity 0.2s;
}

.leaderboard-table th.sorted i {
    opacity: 1;
}

.leaderboard-table td {
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
}

.leaderboard-table tbody tr:hover {
    background: #f8fafc;
}

.rank-cell {
    font-weight: 600;
    color: #667eea;
    text-align: left;
}

.model-cell {
    font-weight: 600;
    color: #1a202c;
    min-width: 250px;
    max-width: 300px;
    text-align: left;
    white-space: nowrap;
}

.organization-cell {
    color: #718096;
    font-size: 0.875rem;
}

.score-cell {
    font-weight: 600;
    font-family: 'Monaco', 'Menlo', monospace;
    text-align: center;
    min-width: 70px;
    color: #2563eb;
}

.date-cell {
    color: #718096;
    font-size: 0.875rem;
}

/* Stats */
.stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #667eea, transparent);
    transition: left 0.5s ease;
}

.stat-card:hover::before {
    left: 100%;
}

.stat-card h3 {
    font-size: 0.875rem;
    color: #718096;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: color 0.3s ease;
}

.stat-card:hover h3 {
    color: #667eea;
}

.stat-card span {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a202c;
    transition: all 0.3s ease;
    display: inline-block;
}

.stat-card:hover span {
    transform: scale(1.05);
    color: #667eea;
}

/* Updating animation */
.stat-card.updating {
    animation: pulse 1s ease-in-out infinite;
}

.stat-card.updating span {
    color: #667eea;
}

@keyframes pulse {
    0% {
        box-shadow: 0 4px 6px rgba(0,0,0,0.05), 0 0 0 0 rgba(102, 126, 234, 0.4);
    }
    50% {
        box-shadow: 0 8px 25px rgba(0,0,0,0.1), 0 0 0 8px rgba(102, 126, 234, 0.1);
    }
    100% {
        box-shadow: 0 4px 6px rgba(0,0,0,0.05), 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

/* Number animation */
@keyframes countUp {
    from {
        transform: translateY(10px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Top 3 highlighting */
.score-cell.top-1 {
    background-color: rgba(255, 215, 0, 0.3) !important; /* Gold/Yellow with transparency */
    font-weight: 700;
    position: relative;
}

.score-cell.top-2 {
    background-color: rgba(59, 130, 246, 0.3) !important; /* Blue with transparency */
    font-weight: 600;
    position: relative;
}

.score-cell.top-3 {
    background-color: rgba(34, 197, 94, 0.3) !important; /* Green with transparency */
    font-weight: 600;
    position: relative;
}

/* Add subtle border for better definition */
.score-cell.top-1::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid rgba(255, 215, 0, 0.5);
    border-radius: 4px;
    pointer-events: none;
}

.score-cell.top-2::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid rgba(59, 130, 246, 0.5);
    border-radius: 4px;
    pointer-events: none;
}

.score-cell.top-3::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid rgba(34, 197, 94, 0.5);
    border-radius: 4px;
    pointer-events: none;
}

/* Hover effects for highlighted cells */
.score-cell.top-1:hover {
    background-color: rgba(255, 215, 0, 0.4) !important;
}

.score-cell.top-2:hover {
    background-color: rgba(59, 130, 246, 0.4) !important;
}

.score-cell.top-3:hover {
    background-color: rgba(34, 197, 94, 0.4) !important;
}

/* Rank column gold, silver, bronze styling */
.rank-cell.rank-gold {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.8), rgba(255, 193, 7, 0.6)) !important;
    color: #8B4513;
    font-weight: 900;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    border: 2px solid #DAA520;
    position: relative;
    border-radius: 6px;
    padding-left: 24px !important;
}

.rank-cell.rank-gold::before {
    content: '🥇';
    position: absolute;
    left: 4px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
}

.rank-cell.rank-silver {
    background: linear-gradient(135deg, rgba(192, 192, 192, 0.8), rgba(169, 169, 169, 0.6)) !important;
    color: #4A4A4A;
    font-weight: 800;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    border: 2px solid #999999;
    position: relative;
    border-radius: 6px;
    padding-left: 24px !important;
}

.rank-cell.rank-silver::before {
    content: '🥈';
    position: absolute;
    left: 4px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
}

.rank-cell.rank-bronze {
    background: linear-gradient(135deg, rgba(205, 127, 50, 0.8), rgba(184, 134, 11, 0.6)) !important;
    color: #FFFFFF;
    font-weight: 800;
    text-shadow: 0 1px 2px rgba(0,0,0,0.4);
    border: 2px solid #8B4513;
    position: relative;
    border-radius: 6px;
    padding-left: 24px !important;
}

.rank-cell.rank-bronze::before {
    content: '🥉';
    position: absolute;
    left: 4px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
}

/* Hover effects for rank cells */
.rank-cell.rank-gold:hover {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.9), rgba(255, 193, 7, 0.7)) !important;
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
}

.rank-cell.rank-silver:hover {
    background: linear-gradient(135deg, rgba(192, 192, 192, 0.9), rgba(169, 169, 169, 0.7)) !important;
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(192, 192, 192, 0.4);
}

.rank-cell.rank-bronze:hover {
    background: linear-gradient(135deg, rgba(205, 127, 50, 0.9), rgba(184, 134, 11, 0.7)) !important;
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(205, 127, 50, 0.4);
}

/* Add transition for smooth effects */
.rank-cell {
    transition: all 0.3s ease;
}

/* Footer */
.footer {
    background: #2d3748;
    color: white;
    padding: 2rem 0;
    margin-top: 3rem;
}

.footer .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-links {
    display: flex;
    gap: 1.5rem;
}

.footer-links a {
    color: #a0aec0;
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.2s;
}

.footer-links a:hover {
    color: white;
}

/* Responsive design */
@media (max-width: 1024px) {
    .nav {
        gap: 0.4rem;
        margin-left: 1.5rem;
    }

    .nav-link {
        padding: 0.7rem 1rem;
        font-size: 0.9rem;
        min-width: 125px;
        height: 46px;
    }

    .nav-link i {
        font-size: 0.95rem;
    }
}

@media (max-width: 768px) {
    .header {
        padding: 1rem 0;
    }

    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .logo h1 {
        font-size: 1.8rem;
    }

    .subtitle {
        font-size: 0.85rem;
    }

    .nav {
        gap: 0.5rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .nav-link {
        padding: 0.6rem 0.8rem;
        font-size: 0.8rem;
        min-width: 100px;
        height: 38px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .controls {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        max-width: none;
    }

    .filters {
        justify-content: center;
    }

    .footer .container {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .nav {
        gap: 0.25rem;
    }

    .nav-link {
        padding: 0.5rem 0.5rem;
        font-size: 0.75rem;
        flex-direction: column;
        gap: 0.25rem;
        min-width: 80px;
        height: 60px;
        justify-content: center;
    }

    .nav-link i {
        font-size: 1.1rem;
    }

    .nav-link span {
        font-size: 0.7rem;
        line-height: 1;
    }

    .logo h1 {
        font-size: 1.5rem;
    }

    .subtitle {
        font-size: 0.8rem;
    }
}

/* Loading animation */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
